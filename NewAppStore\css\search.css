/* Search Page Styles */

/* Search Header */
.search-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.search-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: var(--spacing-xl);
}

/* Search Box */
.search-box-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.search-box {
    display: flex;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.search-box input {
    flex: 1;
    padding: var(--spacing-lg);
    border: none;
    font-size: 1.1rem;
    outline: none;
    color: var(--text-primary);
}

.search-box input::placeholder {
    color: var(--text-tertiary);
}

.search-btn {
    background: var(--primary-color);
    border: none;
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background: var(--primary-dark);
}

.search-icon {
    font-size: 1.2rem;
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestions.show {
    display: block;
}

.suggestion-item {
    padding: var(--spacing-md);
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.suggestion-item:hover {
    background: var(--surface-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* Search Filters */
.search-filters {
    background: var(--surface-color);
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--border-color);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.filter-label {
    font-weight: 500;
    color: var(--text-primary);
    min-width: 80px;
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background: white;
    font-size: 0.9rem;
    min-width: 150px;
}

.filter-clear {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.filter-clear:hover {
    background: var(--danger-dark);
}

/* Search Results */
.search-results {
    padding: var(--spacing-xl) 0;
    min-height: 400px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.results-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.results-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

/* No Results */
.no-results {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-results h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.no-results p {
    color: var(--text-secondary);
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Popular Searches */
.popular-searches {
    background: var(--surface-color);
    padding: var(--spacing-xl) 0;
}

.popular-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tag {
    background: white;
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-full);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tag:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-title {
        font-size: 2rem;
    }
    
    .search-subtitle {
        font-size: 1rem;
    }
    
    .search-box input {
        padding: var(--spacing-md);
        font-size: 1rem;
    }
    
    .search-btn {
        padding: var(--spacing-md);
    }
    
    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .results-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .filter-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-label {
        min-width: auto;
    }
    
    .filter-select {
        width: 100%;
    }
    
    .popular-tags {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .search-header {
        padding: 100px 0 60px;
    }
    
    .search-title {
        font-size: 1.8rem;
    }
    
    .search-box-container {
        margin: 0 var(--spacing-md);
    }
}
