/* Category Page Styles */

/* Category Header */
.category-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 120px 0 60px;
    text-align: center;
    color: white;
}

.category-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Category Navigation */
.category-nav {
    background: white;
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    position: sticky;
    top: 70px;
    z-index: 100;
}

.category-tabs {
    display: flex;
    gap: var(--spacing-sm);
    overflow-x: auto;
    padding-bottom: var(--spacing-xs);
}

.tab-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-full);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.9rem;
    font-weight: 500;
}

.tab-btn:hover {
    background: var(--surface-color);
    border-color: var(--primary-color);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Apps Section */
.apps-section {
    padding: var(--spacing-xl) 0;
    min-height: 500px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.apps-count {
    font-size: 1.1rem;
    color: var(--text-primary);
    font-weight: 500;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sort-controls label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.sort-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background: white;
    font-size: 0.9rem;
    min-width: 120px;
}

/* Apps Grid */
.apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

/* No Apps */
.no-apps {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

.no-apps-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-apps h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.no-apps p {
    color: var(--text-secondary);
}

/* Load More */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.load-more-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
}

/* Category Stats */
.category-stats {
    background: var(--surface-color);
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .category-title {
        font-size: 2rem;
    }
    
    .category-subtitle {
        font-size: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .sort-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .sort-select {
        flex: 1;
        max-width: 200px;
    }
    
    .apps-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .category-tabs {
        justify-content: flex-start;
    }
    
    .tab-btn {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .category-header {
        padding: 100px 0 40px;
    }
    
    .category-title {
        font-size: 1.8rem;
    }
    
    .apps-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: var(--spacing-md);
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .category-nav {
        padding: var(--spacing-md) 0;
    }
    
    .category-tabs {
        gap: var(--spacing-xs);
    }
}
