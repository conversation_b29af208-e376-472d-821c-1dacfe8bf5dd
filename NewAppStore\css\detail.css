/* App Detail Page Styles */

/* App Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 120px 0 60px;
    color: white;
}

.app-header-content {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--spacing-xl);
    align-items: center;
}

.app-icon-container {
    position: relative;
}

.app-icon {
    width: 120px;
    height: 120px;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    object-fit: cover;
}

.app-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.app-info h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-developer {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 500;
}

.app-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.rating-text {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.rating-count {
    opacity: 0.8;
    font-size: 0.9rem;
    margin: 0;
}

.app-badges {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    margin-top: var(--spacing-xs);
}

.badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.price-badge {
    background: var(--success-color);
}

.app-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.download-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: rgba(255, 255, 255, 0.9);
}



/* App Details */
.app-details {
    padding: var(--spacing-xl) 0;
}

.details-grid {
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: var(--spacing-xl);
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* Screenshots */
.screenshots-container {
    display: flex;
    gap: var(--spacing-sm);
    overflow-x: auto;
    padding: var(--spacing-sm) 0;
    scroll-snap-type: x mandatory;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
    max-width: 100%;
    /* 确保容器不会超出父元素宽度 */
    width: 100%;
    box-sizing: border-box;
}

.screenshots-container::-webkit-scrollbar {
    height: 8px;
}

.screenshots-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.screenshots-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.screenshots-container::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

.screenshot {
    width: 120px;
    height: 213px;
    border-radius: var(--border-radius-lg);
    object-fit: cover;
    box-shadow: var(--shadow-md);
    cursor: default;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex-shrink: 0;
    scroll-snap-align: start;
}

.screenshot:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.no-screenshots {
    color: var(--text-secondary);
    font-style: italic;
    text-align: center;
    padding: var(--spacing-xl);
}

.more-screenshots {
    width: 120px;
    height: 213px;
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex-shrink: 0;
    scroll-snap-align: start;
    color: white;
}

.more-screenshots:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.more-screenshots-content {
    text-align: center;
}

.more-icon {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
}

.more-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

.collapse-screenshots {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

.collapse-screenshots:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .screenshots-container {
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) 0;
    }

    .screenshot,
    .more-screenshots {
        width: 120px;
        height: 213px;
    }

    .more-icon {
        font-size: 1.5rem;
    }

    .more-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .screenshot,
    .more-screenshots {
        width: 100px;
        height: 178px;
    }

    .more-icon {
        font-size: 1.2rem;
    }

    .more-text {
        font-size: 0.7rem;
    }
}

/* Description */
.description-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

.description-content.collapsed {
    max-height: 150px;
    overflow: hidden;
    position: relative;
}

.description-content.collapsed::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(transparent, white);
}

.read-more-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-weight: 500;
    margin-top: var(--spacing-sm);
}

/* Sidebar */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.info-card {
    background: var(--surface-color);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

/* Developer Info */
.developer-info {
    text-align: center;
}

.developer-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.developer-apps-btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Rating Breakdown */
.rating-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.rating-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.rating-stars {
    font-size: 0.8rem;
}

.rating-bar {
    flex: 1;
    height: 8px;
    background: var(--border-color);
    border-radius: var(--border-radius-full);
    overflow: hidden;
}

.rating-fill {
    height: 100%;
    background: var(--warning-color);
    transition: width 0.3s ease;
}

.rating-percentage {
    font-size: 0.8rem;
    color: var(--text-secondary);
    min-width: 30px;
}

/* Related Apps */
.related-apps {
    background: var(--surface-color);
    padding: var(--spacing-xl) 0;
}

.related-apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-md);
    max-width: 100%;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .app-icon {
        width: 100px;
        height: 100px;
    }

    .app-info h1 {
        font-size: 2rem;
    }

    .app-actions {
        flex-direction: row;
        justify-content: center;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .screenshots-container {
        justify-content: center;
    }

    .screenshot {
        width: 110px;
        height: 195px;
    }

    .more-screenshots {
        width: 110px;
        height: 195px;
    }

    .related-apps-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .app-header {
        padding: 100px 0 40px;
    }

    .app-info h1 {
        font-size: 1.8rem;
    }

    .app-actions {
        flex-direction: column;
    }

    .download-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
    }

    .screenshot {
        width: 120px;
        height: 200px;
    }

    .info-card {
        padding: var(--spacing-md);
    }
}
