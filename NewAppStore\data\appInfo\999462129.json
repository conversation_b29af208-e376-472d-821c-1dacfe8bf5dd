{"isGameCenterEnabled": false, "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": ["Unrestricted Web Access"], "kind": "software", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/48/43/3e/48433e77-92cf-3631-4c8f-54602de2d5cb/5806a05a-2f9c-4420-9b40-c56edb60f3cc_iphoneSE-en-2xwidth_01.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/97/a8/c1/97a8c184-371b-53b6-3c50-e4f70fde279a/4236a449-d56b-4686-9cae-2b3fd847664c_iphoneSE-en-2xwidth_02.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/c5/ac/d8/c5acd8b2-a890-bdcf-d601-20d08b12c94d/269b0083-5739-47e0-8f3c-508cc93e4e17_iphone-se-3.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/51/e5/db/51e5dbfe-b915-363f-fe8e-f682ab002f9f/3d99d9e0-03eb-48d1-8914-f216efd1c9aa_iphone-se-4.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/26/71/de/2671defd-1be4-f256-81f6-6a01c2807539/24774dbf-22fb-4180-9948-555ab862576d_iphone-se-5.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/ea/5b/2e/ea5b2e08-8b27-8095-ae05-bfbfcd1ec914/4d2698d8-b384-4cf4-b32b-892288b8723e_iphone-se-6.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/b4/39/94/b4399499-55df-bb35-8009-11cf7bf28046/cfda92e4-0da7-44fd-b547-bee8066ca8da_ipadpro-en-2xwidth_01.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/2b/8b/ef/2b8befd8-8c0c-7224-f4bc-bbc06394a15b/682a719e-9d98-4b42-a5cc-3360d0851f0b_ipadpro-en-2xwidth_02.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/49/eb/ab/49ebab97-0b4e-266f-3acb-c24d3756889c/8adec8bd-474b-4215-bdf4-114e8444f242_ipadpro-en-screen3.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/6a/a7/2f/6aa72fa2-157b-4fbf-1d54-2a2cc17db185/ae600314-b2ba-4b80-be21-918ced98f425_ipadpro-en-screen4.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/da/92/aa/da92aad3-5e13-6b93-a249-da0ec113c794/de59a01d-1a48-40e8-9340-f72307c3b4c3_ipadpro-en-screen5.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/98/1d/1f/981d1f4b-7e39-a7ea-2dc9-2cd4dabbe0c2/22e35221-529e-449e-9d75-989e05235587_ipadpro-en-screen6.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/fe/01/1e/fe011e3b-49c6-4b0f-6ee4-047d23853ce5/AppIcon-iwebtv-0-0-1x_U007epad-0-0-0-9-0-0-85-220.png/60x60bb.jpg", "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/fe/01/1e/fe011e3b-49c6-4b0f-6ee4-047d23853ce5/AppIcon-iwebtv-0-0-1x_U007epad-0-0-0-9-0-0-85-220.png/512x512bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/fe/01/1e/fe011e3b-49c6-4b0f-6ee4-047d23853ce5/AppIcon-iwebtv-0-0-1x_U007epad-0-0-0-9-0-0-85-220.png/100x100bb.jpg", "artistViewUrl": "https://apps.apple.com/us/developer/swishly-inc/id396072075?uo=4", "minimumOsVersion": "16.6", "artistId": 396072075, "artistName": "Swishly inc", "genres": ["Photo & Video", "Entertainment"], "price": 0, "bundleId": "com.swishly.webtv", "genreIds": ["6008", "6016"], "isVppDeviceBasedLicensingEnabled": true, "primaryGenreName": "Photo & Video", "primaryGenreId": 6008, "trackId": 999462129, "trackName": "Cast Web Videos to TV - iWebTV", "currentVersionReleaseDate": "2025-06-01T02:07:01Z", "releaseDate": "2015-07-11T08:46:40Z", "releaseNotes": "- Critical fix for freeze affecting some web pages (rare)\n- Ad-blocker update", "version": "2.0.95", "wrapperType": "software", "currency": "USD", "description": "Best Casting App! Cast any online video to your TV.\n\niWebTV® works with any TV equipped with Chromecast® + Roku® + Fire TV® + Apple TV® (4th Gen) + Samsung TVs (2018 and later models).\n\n*** Feature Highlights ***\n\n• HD resolution supported (1080p and up to 4K depending on the device)\n• Unlike mirroring apps, iWebTV sends the actual video stream to your TV (Much better image quality & overall experience).\n• Advanced browser, supports multiple browser tabs, blocks or hides spammy popups, search from the URL bar, ad blocker, browsing history etc...\n• Subtitle auto-detect + Movie/TV Subtitle library\n• Live streams support\n• Video preview to instantly locate your favorite scenes.\n• Binge-ready: queue up several videos, and enjoy.\n• Set your own home page, bookmark web page or videos.\n• Full playback controls, even after exiting the app (from the lock screen).\n• Privacy modes\n\n\nSome of the features above require in-app purchase\n\niWebTV actually plays the video on your media player which results in a much higher quality picture than apps that mirror your screen.\n\n\n\n**** Notes ****\n\n(1) Some premium features require in-app purchases.\n(2) Excluding video formats incompatible with iOS (flash).\n(3) While most video websites work well, email us from the app menu if you experience any issues: > “Get Help” > “Frequent Questions” > “Need more help? (Other Issues)”> “Contact Support” (opens email).\n\nSmart TV from most TV manufacturers will work with this app without any preliminary setup. Just start the app, choose a video & hit the cast button! This includes TVs from Samsung, TCL, Vizio, Sony, Hisense, Insigna, Sharp, Philips and others.\n\n\n\n\n**** Legal ****\n\niWebTV™ is a trademark of Swishly Inc.\n\"Chromecast\" is a trademark of Google LLC.\n\"Fire TV\" is a trademark of Amazon Technologies, Inc.\n\"Roku\" is a trademark of Roku Inc.\n\"Apple TV\" is a trademark of Apple Inc.\n\n\n\n\nTerms:\n\n• Privacy Policy: http://www.swishly.com/webtv/privacy-policy.html\n• Terms of Use: http://www.swishly.com/webtv/terms-of-use.html\n\niWebTV offers a variety of upgrades, one of which is subscription-based (\"Premium Services\" $0.99/month or $9.99/year). With this subscription you will get the benefit of all premium features in addition to 2 premium services (Cloud Proxy Streaming  + Unlimited subtitle downloads)", "userRatingCountForCurrentVersion": 123298, "averageUserRatingForCurrentVersion": 4.73925, "averageUserRating": 4.73925, "trackCensoredName": "Cast Web Videos to TV - iWebTV", "languageCodesISO2A": ["EN", "ES"], "fileSizeBytes": "55712768", "formattedPrice": "Free", "contentAdvisoryRating": "17+", "trackViewUrl": "https://apps.apple.com/us/app/cast-web-videos-to-tv-iwebtv/id999462129?uo=4", "trackContentRating": "17+", "sellerName": "Swishly inc", "userRatingCount": 123298}