/* Index Page Styles */

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--bg-tertiary);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.brand-icon {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    font-weight: 500;
    color: var(--text-secondary);
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    overflow: hidden;
    position: relative;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-content {
    z-index: 2;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-actions .btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 400px;
}

.floating-cards {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    width: 80px;
    height: 80px;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    animation: float 6s ease-in-out infinite;
}

.floating-card i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 10%;
    right: 20%;
    animation-delay: 1.5s;
}

.card-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 3s;
}

.card-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: 0;
}

/* Featured Categories */
.featured-categories {
    padding: var(--spacing-3xl) 0;
    background: var(--bg-primary);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.category-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 1px solid var(--bg-tertiary);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.category-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.category-icon i {
    font-size: var(--font-size-2xl);
    color: var(--text-white);
}

.category-name {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.category-desc {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.category-apps {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
}

/* App Cards in Categories */
.mini-app-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    text-decoration: none;
}

.mini-app-card:hover {
    background: var(--bg-secondary);
    transform: scale(1.05);
}

.mini-app-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-xs);
    object-fit: cover;
}

.mini-app-name {
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-align: center;
    color: var(--text-primary);
    line-height: 1.2;
}

/* Trending Apps */
.trending-apps {
    padding: var(--spacing-3xl) 0;
    background: var(--bg-secondary);
}

.trending-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.trending-app-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    text-decoration: none;
    display: block;
}

.trending-app-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.trending-app-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.trending-app-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    object-fit: cover;
}

.trending-app-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.trending-app-info p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: 0;
}

.trending-app-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    color: #fbbf24;
    font-size: var(--font-size-sm);
}

.rating-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.trending-app-desc {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: var(--text-white);
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-xl);
}

.footer-brand .brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.footer-desc {
    color: #9ca3af;
    margin-bottom: 0;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
}

.footer-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-white);
}

.footer-section a {
    display: block;
    color: #9ca3af;
    margin-bottom: var(--spacing-sm);
    transition: color var(--transition-fast);
}

.footer-section a:hover {
    color: var(--text-white);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: var(--spacing-xl);
    text-align: center;
}

.footer-bottom p {
    color: #9ca3af;
    margin-bottom: 0;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: var(--text-white);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--bg-primary);
        flex-direction: column;
        justify-content: flex-start;
        padding-top: var(--spacing-xl);
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-actions {
        justify-content: center;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .trending-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .footer-links {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-actions .btn {
        width: 100%;
        max-width: 250px;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .category-apps {
        grid-template-columns: repeat(2, 1fr);
    }
}
