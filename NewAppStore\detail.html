<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Details - AppVault</title>
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/detail.css">
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.html">
                    <span class="brand-icon">📱</span>
                    <span class="brand-text">AppVault</span>
                </a>
            </div>

            <div class="nav-menu" id="navMenu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="search.html" class="nav-link">Search</a>
                <a href="type.html" class="nav-link">Categories</a>
            </div>

            <button class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- App Header -->
    <section class="app-header">
        <div class="container">
            <div class="app-header-content">
                <div class="app-icon-container">
                    <img id="appIcon" src="" alt="App Icon" class="app-icon">
                </div>
                
                <div class="app-info">
                    <h1 class="app-name" id="appName">Loading...</h1>
                    <p class="app-developer" id="appDeveloper">Developer</p>
                    <div class="app-rating">
                        <div class="stars" id="appStars"></div>
                        <span class="rating-text" id="ratingText">0.0</span>
                        <span class="rating-count" id="ratingCount">(0 reviews)</span>
                    </div>
                    <div class="app-badges">
                        <span class="badge" id="categoryBadge">Category</span>
                        <span class="badge price-badge" id="priceBadge">Free</span>
                    </div>
                </div>
                
                <div class="app-actions">
                    <a href="https://apps.apple.com/app/id541933937" target="_blank" class="btn-primary download-btn" id="downloadBtn">
                        <span class="download-icon">⬇️</span>
                        Download
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- App Details -->
    <section class="app-details">
        <div class="container">
            <div class="details-grid">
                <!-- Main Content -->
                <div class="main-content">
                    <!-- Screenshots -->
                    <div class="screenshots-section">
                        <h3 class="section-title">Screenshots</h3>
                        <div class="screenshots-container" id="screenshotsContainer">
                            <!-- Screenshots will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="description-section">
                        <h3 class="section-title">Description</h3>
                        <div class="description-content" id="descriptionContent">
                            <p>Loading description...</p>
                        </div>
                        <button class="read-more-btn" id="readMoreBtn" style="display: none;">Read More</button>
                    </div>
                    
                    <!-- What's New -->
                    <div class="whats-new-section" id="whatsNewSection" style="display: none;">
                        <h3 class="section-title">What's New</h3>
                        <div class="whats-new-content" id="whatsNewContent"></div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="sidebar">
                    <!-- App Info -->
                    <div class="info-card">
                        <h4 class="card-title">App Information</h4>
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">Version</span>
                                <span class="info-value" id="appVersion">1.0.0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Size</span>
                                <span class="info-value" id="appSize">0 MB</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Category</span>
                                <span class="info-value" id="appCategory">Unknown</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Compatibility</span>
                                <span class="info-value" id="appCompatibility">iOS 12.0+</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Languages</span>
                                <span class="info-value" id="appLanguages">English</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Age Rating</span>
                                <span class="info-value" id="appAgeRating">4+</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Developer Info -->
                    <div class="info-card">
                        <h4 class="card-title">Developer</h4>
                        <div class="developer-info">
                            <p class="developer-name" id="developerName">Developer Name</p>
                            <button class="btn-secondary developer-apps-btn" id="developerAppsBtn">
                                View More Apps
                            </button>
                        </div>
                    </div>
                    
                    <!-- Ratings Breakdown -->
                    <div class="info-card">
                        <h4 class="card-title">Ratings & Reviews</h4>
                        <div class="rating-breakdown" id="ratingBreakdown">
                            <!-- Rating breakdown will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Apps -->
    <section class="related-apps">
        <div class="container">
            <h3 class="section-title">You Might Also Like</h3>
            <div class="related-apps-grid" id="relatedAppsGrid">
                <!-- Related apps will be populated here -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <span class="brand-icon">📱</span>
                    <span class="brand-text">AppVault</span>
                </div>
                <div class="footer-links">
                    <a href="Privacy.html">Privacy Policy</a>
                    <a href="Terms.html">Terms of Service</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AppVault. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading app details...</p>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/detail.js"></script>
</body>
</html>
