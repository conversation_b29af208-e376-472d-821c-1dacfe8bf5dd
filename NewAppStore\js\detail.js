// App Detail Page JavaScript

class AppDetailPage {
    constructor() {
        this.appId = null;
        this.appData = null;
        this.allApps = [];
        this.categories = [];
        
        this.init();
    }
    
    async init() {
        try {
            this.appId = this.getAppIdFromURL();
            console.log('App ID from URL:', this.appId);

            if (!this.appId) {
                throw new Error('No app ID provided');
            }

            this.showLoading();
            await this.loadData();
            await this.loadAppDetails();
            this.renderAppDetails();
            this.loadRelatedApps();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize app detail page:', error);
            this.showError('Failed to load app details');
            this.hideLoading();
        }
    }
    
    getAppIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id') || urlParams.get('appId');
    }
    
    async loadData() {
        try {
            // Load categories to get all apps
            const categoriesData = await loadData('./data/typeList.json');

            // The data structure is { metadata: {...}, rankInfo: [...] }
            this.allApps = categoriesData.rankInfo || [];

            // Group apps by type for categories
            this.categories = {};
            this.allApps.forEach(app => {
                if (!this.categories[app.type]) {
                    this.categories[app.type] = {
                        name: app.type,
                        type: app.type,
                        list: []
                    };
                }
                this.categories[app.type].list.push(app);
            });

            console.log(`Loaded ${this.allApps.length} apps for detail page`);
        } catch (error) {
            console.error('Error loading app data:', error);
            throw error;
        }
    }
    
    async loadAppDetails() {
        try {
            // Always try to load the detailed individual app file first
            try {
                console.log(`Trying to load individual app file: ./data/appInfo/${this.appId}.json`);
                this.appData = await loadData(`./data/appInfo/${this.appId}.json`);
                console.log('Loaded individual app file:', this.appData);
            } catch (fileError) {
                console.log('Individual app file not found, falling back to rankInfo data');
                // Fall back to rankInfo data
                this.appData = this.allApps.find(app =>
                    app.appId.toString() === this.appId ||
                    app.appId === parseInt(this.appId)
                );
                console.log('Found app in rankInfo:', this.appData);
            }

            if (!this.appData) {
                throw new Error('App not found');
            }

            console.log('Final app details:', this.appData);
        } catch (error) {
            console.error('Error loading app details:', error);
            throw error;
        }
    }
    
    renderAppDetails() {
        console.log('Rendering app details with data:', this.appData);
        this.updatePageTitle();
        this.renderAppHeader();
        this.renderAppInfo();
        this.renderDescription();
        this.renderScreenshots();
        this.renderRatingBreakdown();
    }
    
    updatePageTitle() {
        const appName = this.appData.appName || this.appData.trackName || this.appData.name || 'App';
        document.title = `${appName} - AppVault`;
    }
    
    renderAppHeader() {
        // App icon - handle different data structures
        const appIcon = document.getElementById('appIcon');
        const iconUrl = this.appData.icon || this.appData.artworkUrl100 || this.appData.artworkUrl60 || 'images/app-placeholder.svg';
        appIcon.src = iconUrl;
        appIcon.alt = this.appData.appName || this.appData.trackName || this.appData.name || 'App';

        // App name and developer - handle different data structures
        const appName = this.appData.appName || this.appData.trackName || this.appData.trackCensoredName || this.appData.name || 'Unknown App';
        const developer = this.appData.publisher || this.appData.artistName || this.appData.developer || 'Unknown Developer';

        document.getElementById('appName').textContent = appName;
        document.getElementById('appDeveloper').textContent = developer;

        // Rating - handle different data structures
        const rating = parseFloat(this.appData.rating || this.appData.averageUserRating || 0);
        const reviewCount = this.appData.num || this.appData.userRatingCount || this.appData.userRatingCountForCurrentVersion || 0;

        document.getElementById('appStars').innerHTML = generateStarRating(rating);
        document.getElementById('ratingText').textContent = rating.toFixed(1);
        document.getElementById('ratingCount').textContent = `(${reviewCount.toLocaleString()} reviews)`;

        // Badges - handle different data structures
        const category = this.appData.type || this.appData.primaryGenreName || this.appData.categoryName || 'App';
        document.getElementById('categoryBadge').textContent = category;

        const priceBadge = document.getElementById('priceBadge');
        const price = this.appData.price || this.appData.formattedPrice;
        if (!price || price === 0 || price === '0' || (typeof price === 'string' && price.toLowerCase().includes('free'))) {
            priceBadge.textContent = 'Free';
            priceBadge.className = 'badge price-badge';
        } else {
            priceBadge.textContent = typeof price === 'string' ? price : `$${price}`;
            priceBadge.className = 'badge price-badge paid';
        }
    }
    
    renderAppInfo() {
        // Basic info - handle different data structures
        document.getElementById('appVersion').textContent = this.appData.version || '1.0.0';

        // Format file size
        const fileSize = this.appData.size || this.appData.fileSizeBytes;
        let formattedSize = 'Unknown';
        if (fileSize) {
            if (typeof fileSize === 'string' && fileSize.includes('MB')) {
                formattedSize = fileSize;
            } else {
                const sizeInBytes = parseInt(fileSize);
                if (sizeInBytes) {
                    const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(1);
                    formattedSize = `${sizeInMB} MB`;
                }
            }
        }
        document.getElementById('appSize').textContent = formattedSize;

        const category = this.appData.type || this.appData.primaryGenreName || this.appData.categoryName || 'Unknown';
        document.getElementById('appCategory').textContent = category;

        const compatibility = this.appData.compatibility || (this.appData.minimumOsVersion ? `iOS ${this.appData.minimumOsVersion}+` : 'iOS 12.0+');
        document.getElementById('appCompatibility').textContent = compatibility;

        // Handle languages
        let languages = 'English';
        if (this.appData.languages) {
            languages = this.appData.languages;
        } else if (this.appData.languageCodesISO2A && this.appData.languageCodesISO2A.length > 0) {
            languages = this.appData.languageCodesISO2A.join(', ');
        }
        document.getElementById('appLanguages').textContent = languages;

        const ageRating = this.appData.ageRating || this.appData.contentAdvisoryRating || this.appData.trackContentRating || '4+';
        document.getElementById('appAgeRating').textContent = ageRating;

        // Developer info
        const developer = this.appData.publisher || this.appData.artistName || this.appData.sellerName || this.appData.developer || 'Unknown Developer';
        document.getElementById('developerName').textContent = developer;
    }
    
    renderDescription() {
        const descriptionContent = document.getElementById('descriptionContent');
        const readMoreBtn = document.getElementById('readMoreBtn');

        // Force check for description field
        let description = '';
        if (this.appData && this.appData.description) {
            description = this.appData.description;
        } else if (this.appData && this.appData.subtitle) {
            description = this.appData.subtitle;
        } else {
            description = 'No description available.';
        }

        console.log('Description data:', description);
        console.log('App data keys:', Object.keys(this.appData || {}));

        // Format description with line breaks
        const formattedDescription = description.replace(/\n/g, '<br>');
        descriptionContent.innerHTML = `<p>${formattedDescription}</p>`;

        // Check if description is long enough to need "Read More"
        if (description.length > 300) {
            descriptionContent.classList.add('collapsed');
            readMoreBtn.style.display = 'block';

            readMoreBtn.addEventListener('click', () => {
                descriptionContent.classList.toggle('collapsed');
                readMoreBtn.textContent = descriptionContent.classList.contains('collapsed')
                    ? 'Read More' : 'Read Less';
            });
        }

        // What's New section - handle different data structures
        const whatsNew = this.appData.whatsNew || this.appData.releaseNotes;
        if (whatsNew) {
            const whatsNewSection = document.getElementById('whatsNewSection');
            const whatsNewContent = document.getElementById('whatsNewContent');
            const formattedWhatsNew = whatsNew.replace(/\n/g, '<br>');
            whatsNewContent.innerHTML = `<p>${formattedWhatsNew}</p>`;
            whatsNewSection.style.display = 'block';
        }
    }
    
    renderScreenshots() {
        const screenshotsContainer = document.getElementById('screenshotsContainer');

        // Force check for screenshot fields
        let screenshots = [];
        if (this.appData && this.appData.screenshotUrls && Array.isArray(this.appData.screenshotUrls)) {
            screenshots = this.appData.screenshotUrls;
        } else if (this.appData && this.appData.screenshots && Array.isArray(this.appData.screenshots)) {
            screenshots = this.appData.screenshots;
        } else if (this.appData && this.appData.ipadScreenshotUrls && Array.isArray(this.appData.ipadScreenshotUrls)) {
            screenshots = this.appData.ipadScreenshotUrls;
        }

        console.log('Screenshots data:', screenshots);

        if (screenshots && screenshots.length > 0) {
            // 限制显示前5张截图，避免页面过长
            const displayScreenshots = screenshots.slice(0, 5);
            const hasMore = screenshots.length > 5;

            let screenshotsHTML = displayScreenshots.map((screenshot, index) => `
                <img src="${screenshot}" alt="Screenshot ${index + 1}" class="screenshot"
                     onclick="this.requestFullscreen ? this.requestFullscreen() : null"
                     loading="lazy">
            `).join('');

            // 如果有更多截图，添加一个"更多"指示器
            if (hasMore) {
                screenshotsHTML += `
                    <div class="more-screenshots" onclick="this.showAllScreenshots()">
                        <div class="more-screenshots-content">
                            <span class="more-icon">+${screenshots.length - 5}</span>
                            <span class="more-text">更多截图</span>
                        </div>
                    </div>
                `;
            }

            screenshotsContainer.innerHTML = screenshotsHTML;
        } else {
            screenshotsContainer.innerHTML = '<p class="no-screenshots">No screenshots available</p>';
        }
    }
    
    renderRatingBreakdown() {
        const ratingBreakdown = document.getElementById('ratingBreakdown');
        
        // Generate mock rating breakdown based on overall rating
        const overallRating = parseFloat(this.appData.rating) || 0;
        const breakdown = this.generateRatingBreakdown(overallRating);
        
        ratingBreakdown.innerHTML = breakdown.map((item, index) => `
            <div class="rating-row">
                <span class="rating-stars">${'★'.repeat(5 - index)}${'☆'.repeat(index)}</span>
                <div class="rating-bar">
                    <div class="rating-fill" style="width: ${item.percentage}%"></div>
                </div>
                <span class="rating-percentage">${item.percentage}%</span>
            </div>
        `).join('');
    }
    
    generateRatingBreakdown(overallRating) {
        // Generate realistic rating distribution based on overall rating
        const base = Math.max(0, overallRating - 2);
        return [
            { stars: 5, percentage: Math.round(base * 20 + Math.random() * 20) },
            { stars: 4, percentage: Math.round(base * 15 + Math.random() * 15) },
            { stars: 3, percentage: Math.round(base * 10 + Math.random() * 10) },
            { stars: 2, percentage: Math.round(base * 5 + Math.random() * 5) },
            { stars: 1, percentage: Math.round(base * 2 + Math.random() * 3) }
        ];
    }
    
    loadRelatedApps() {
        const relatedAppsGrid = document.getElementById('relatedAppsGrid');

        // Get current app category
        const currentCategory = this.appData.type || this.appData.primaryGenreName || this.appData.categoryName;
        const currentAppId = this.appData.appId || this.appData.trackId;

        // Find apps in the same category
        const sameCategory = this.allApps.filter(app => {
            const appCategory = app.type || app.primaryGenreName || app.categoryName;
            const appId = app.appId || app.trackId;
            return appCategory === currentCategory && appId !== currentAppId;
        });

        // Get random 6 apps, or all available if less than 6
        const relatedApps = sameCategory
            .sort(() => Math.random() - 0.5)
            .slice(0, 6);

        if (relatedApps.length > 0) {
            relatedAppsGrid.innerHTML = relatedApps.map(app =>
                createAppCard(app, 'mini')
            ).join('');
        } else {
            // If no related apps found, show random apps
            const randomApps = this.allApps
                .filter(app => (app.appId || app.trackId) !== currentAppId)
                .sort(() => Math.random() - 0.5)
                .slice(0, 6);

            relatedAppsGrid.innerHTML = randomApps.map(app =>
                createAppCard(app, 'mini')
            ).join('');
        }

        // Initialize lazy loading for related apps
        setTimeout(() => {
            initializeLazyLoading();
        }, 100);
    }
    
    setupEventListeners() {
        const downloadBtn = document.getElementById('downloadBtn');
        const shareBtn = document.getElementById('shareBtn');
        const developerAppsBtn = document.getElementById('developerAppsBtn');
        
        // Download button
        downloadBtn.addEventListener('click', () => {
            this.handleDownload();
        });
        
        // Share button
        shareBtn.addEventListener('click', () => {
            this.handleShare();
        });
        
        // Developer apps button
        developerAppsBtn.addEventListener('click', () => {
            this.viewDeveloperApps();
        });
    }
    
    handleDownload() {
        // In a real app, this would handle the actual download
        const appName = this.appData.appName || this.appData.trackName || this.appData.name || 'this app';
        showNotification(`Download started for ${appName}`, 'success');
    }

    handleShare() {
        const appName = this.appData.appName || this.appData.trackName || this.appData.name || 'this app';
        if (navigator.share) {
            navigator.share({
                title: appName,
                text: `Check out ${appName} on AppVault`,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('Link copied to clipboard', 'success');
            }).catch(() => {
                showNotification('Unable to copy link', 'error');
            });
        }
    }

    viewDeveloperApps() {
        const developer = this.appData.publisher || this.appData.artistName || this.appData.developer;
        if (developer) {
            window.location.href = `search.html?q=${encodeURIComponent(developer)}`;
        }
    }
    
    showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.remove('hidden');
    }
    
    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.add('hidden');
    }
    
    showError(message) {
        showNotification(message, 'error');
        // Redirect to home page after error
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 3000);
    }
}

// Initialize app detail page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing AppDetailPage');
    new AppDetailPage();
});
