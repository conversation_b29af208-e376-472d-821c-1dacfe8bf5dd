// App Detail Page JavaScript

class AppDetailPage {
    constructor() {
        this.appId = null;
        this.appData = null;
        this.allApps = [];
        this.categories = [];
        
        this.init();
    }
    
    async init() {
        try {
            this.appId = this.getAppIdFromURL();
            console.log('App ID from URL:', this.appId);

            if (!this.appId) {
                throw new Error('No app ID provided');
            }

            this.showLoading();
            await this.loadData();
            await this.loadAppDetails();
            this.renderAppDetails();
            this.loadRelatedApps();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize app detail page:', error);
            this.showError('Failed to load app details');
            this.hideLoading();
        }
    }
    
    getAppIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id') || urlParams.get('appId');
    }
    
    async loadData() {
        try {
            // Load categories to get all apps
            const categoriesData = await loadData('./data/typeList.json');

            // The data structure is { metadata: {...}, rankInfo: [...] }
            this.allApps = categoriesData.rankInfo || [];

            // Group apps by type for categories
            this.categories = {};
            this.allApps.forEach(app => {
                if (!this.categories[app.type]) {
                    this.categories[app.type] = {
                        name: app.type,
                        type: app.type,
                        list: []
                    };
                }
                this.categories[app.type].list.push(app);
            });

            console.log(`Loaded ${this.allApps.length} apps for detail page`);
        } catch (error) {
            console.error('Error loading app data:', error);
            throw error;
        }
    }
    
    async loadAppDetails() {
        try {
            // Always try to load the detailed individual app file first
            try {
                console.log(`Trying to load individual app file: ./data/appInfo/${this.appId}.json`);
                this.appData = await loadData(`./data/appInfo/${this.appId}.json`);
                console.log('Loaded individual app file:', this.appData);
            } catch (fileError) {
                console.log('Individual app file not found, falling back to rankInfo data');
                // Fall back to rankInfo data
                this.appData = this.allApps.find(app =>
                    app.appId.toString() === this.appId ||
                    app.appId === parseInt(this.appId)
                );
                console.log('Found app in rankInfo:', this.appData);
            }

            if (!this.appData) {
                throw new Error('App not found');
            }

            console.log('Final app details:', this.appData);
        } catch (error) {
            console.error('Error loading app details:', error);
            throw error;
        }
    }
    
    renderAppDetails() {
        console.log('Rendering app details with data:', this.appData);
        this.updatePageTitle();
        this.renderAppHeader();
        this.renderAppInfo();
        this.renderDescription();
        this.renderScreenshots();
    }
    
    updatePageTitle() {
        const appName = this.appData.appName || this.appData.trackName || this.appData.name || 'App';
        document.title = `${appName} - AppVault`;
    }
    
    renderAppHeader() {
        // App icon - handle different data structures
        const appIcon = document.getElementById('appIcon');
        const iconUrl = this.appData.icon || this.appData.artworkUrl100 || this.appData.artworkUrl60 || 'images/app-placeholder.svg';
        appIcon.src = iconUrl;
        appIcon.alt = this.appData.appName || this.appData.trackName || this.appData.name || 'App';

        // App name and developer - handle different data structures
        const appName = this.appData.appName || this.appData.trackName || this.appData.trackCensoredName || this.appData.name || 'Unknown App';
        const developer = this.appData.publisher || this.appData.artistName || this.appData.developer || 'Unknown Developer';

        document.getElementById('appName').textContent = appName;
        document.getElementById('appDeveloper').textContent = developer;

        // Rating - handle different data structures
        const rating = parseFloat(this.appData.rating || this.appData.averageUserRating || 0);
        const reviewCount = this.appData.num || this.appData.userRatingCount || this.appData.userRatingCountForCurrentVersion || 0;

        document.getElementById('appStars').innerHTML = generateStarRating(rating);
        document.getElementById('ratingText').textContent = rating.toFixed(1);
        document.getElementById('ratingCount').textContent = `(${reviewCount.toLocaleString()} reviews)`;

        // Badges - handle different data structures
        const category = this.appData.type || this.appData.primaryGenreName || this.appData.categoryName || 'App';
        document.getElementById('categoryBadge').textContent = category;

        const priceBadge = document.getElementById('priceBadge');
        const price = this.appData.price || this.appData.formattedPrice;
        if (!price || price === 0 || price === '0' || (typeof price === 'string' && price.toLowerCase().includes('free'))) {
            priceBadge.textContent = 'Free';
            priceBadge.className = 'badge price-badge';
        } else {
            priceBadge.textContent = typeof price === 'string' ? price : `$${price}`;
            priceBadge.className = 'badge price-badge paid';
        }
    }
    
    renderAppInfo() {
        // Basic info - handle different data structures
        document.getElementById('appVersion').textContent = this.appData.version || '1.0.0';

        // Format file size
        const fileSize = this.appData.size || this.appData.fileSizeBytes;
        let formattedSize = 'Unknown';
        if (fileSize) {
            if (typeof fileSize === 'string' && fileSize.includes('MB')) {
                formattedSize = fileSize;
            } else {
                const sizeInBytes = parseInt(fileSize);
                if (sizeInBytes) {
                    const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(1);
                    formattedSize = `${sizeInMB} MB`;
                }
            }
        }
        document.getElementById('appSize').textContent = formattedSize;

        const category = this.appData.type || this.appData.primaryGenreName || this.appData.categoryName || 'Unknown';
        document.getElementById('appCategory').textContent = category;

        const compatibility = this.appData.compatibility || (this.appData.minimumOsVersion ? `iOS ${this.appData.minimumOsVersion}+` : 'iOS 12.0+');
        document.getElementById('appCompatibility').textContent = compatibility;

        // Handle languages
        let languages = 'English';
        if (this.appData.languages) {
            languages = this.appData.languages;
        } else if (this.appData.languageCodesISO2A && this.appData.languageCodesISO2A.length > 0) {
            languages = this.appData.languageCodesISO2A.join(', ');
        }
        document.getElementById('appLanguages').textContent = languages;

        const ageRating = this.appData.ageRating || this.appData.contentAdvisoryRating || this.appData.trackContentRating || '4+';
        document.getElementById('appAgeRating').textContent = ageRating;

        // Developer info
        const developer = this.appData.publisher || this.appData.artistName || this.appData.sellerName || this.appData.developer || 'Unknown Developer';
        document.getElementById('developerName').textContent = developer;
    }
    
    renderDescription() {
        const descriptionContent = document.getElementById('descriptionContent');
        const readMoreBtn = document.getElementById('readMoreBtn');

        // Force check for description field
        let description = '';
        if (this.appData && this.appData.description) {
            description = this.appData.description;
        } else if (this.appData && this.appData.subtitle) {
            description = this.appData.subtitle;
        } else {
            description = 'No description available.';
        }

        console.log('Description data:', description);
        console.log('App data keys:', Object.keys(this.appData || {}));

        // Format description with line breaks
        const formattedDescription = description.replace(/\n/g, '<br>');
        descriptionContent.innerHTML = `<p>${formattedDescription}</p>`;

        // Check if description is long enough to need "Read More"
        if (description.length > 300) {
            descriptionContent.classList.add('collapsed');
            readMoreBtn.style.display = 'block';

            readMoreBtn.addEventListener('click', () => {
                descriptionContent.classList.toggle('collapsed');
                readMoreBtn.textContent = descriptionContent.classList.contains('collapsed')
                    ? 'Read More' : 'Read Less';
            });
        }

        // What's New section - handle different data structures
        const whatsNew = this.appData.whatsNew || this.appData.releaseNotes;
        if (whatsNew) {
            const whatsNewSection = document.getElementById('whatsNewSection');
            const whatsNewContent = document.getElementById('whatsNewContent');
            const formattedWhatsNew = whatsNew.replace(/\n/g, '<br>');
            whatsNewContent.innerHTML = `<p>${formattedWhatsNew}</p>`;
            whatsNewSection.style.display = 'block';
        }
    }
    
    renderScreenshots() {
        const screenshotsContainer = document.getElementById('screenshotsContainer');

        // Force check for screenshot fields
        let screenshots = [];
        if (this.appData && this.appData.screenshotUrls && Array.isArray(this.appData.screenshotUrls)) {
            screenshots = this.appData.screenshotUrls;
        } else if (this.appData && this.appData.screenshots && Array.isArray(this.appData.screenshots)) {
            screenshots = this.appData.screenshots;
        } else if (this.appData && this.appData.ipadScreenshotUrls && Array.isArray(this.appData.ipadScreenshotUrls)) {
            screenshots = this.appData.ipadScreenshotUrls;
        }

        console.log('Screenshots data:', screenshots);

        if (screenshots && screenshots.length > 0) {
            // 存储所有截图供后续使用
            this.allScreenshots = screenshots;
            this.showingAllScreenshots = false;

            this.renderScreenshotDisplay();
        } else {
            screenshotsContainer.innerHTML = '<p class="no-screenshots">No screenshots available</p>';
        }
    }

    renderScreenshotDisplay() {
        const screenshotsContainer = document.getElementById('screenshotsContainer');
        const screenshots = this.allScreenshots;

        if (this.showingAllScreenshots) {
            // 显示所有截图，但保持在容器内滚动
            screenshotsContainer.innerHTML = screenshots.map((screenshot, index) => `
                <img src="${screenshot}" alt="Screenshot ${index + 1}" class="screenshot" loading="lazy">
            `).join('');

            // 添加"收起"按钮
            const collapseBtn = document.createElement('div');
            collapseBtn.className = 'more-screenshots collapse-screenshots';
            collapseBtn.innerHTML = `
                <div class="more-screenshots-content">
                    <span class="more-icon">−</span>
                    <span class="more-text">收起</span>
                </div>
            `;
            collapseBtn.addEventListener('click', () => this.toggleScreenshots());
            screenshotsContainer.appendChild(collapseBtn);
        } else {
            // 只显示前4张截图，为"更多"按钮留出空间
            const displayScreenshots = screenshots.slice(0, 4);
            const hasMore = screenshots.length > 4;

            screenshotsContainer.innerHTML = displayScreenshots.map((screenshot, index) => `
                <img src="${screenshot}" alt="Screenshot ${index + 1}" class="screenshot" loading="lazy">
            `).join('');

            // 如果有更多截图，添加"更多"按钮
            if (hasMore) {
                const moreBtn = document.createElement('div');
                moreBtn.className = 'more-screenshots';
                moreBtn.innerHTML = `
                    <div class="more-screenshots-content">
                        <span class="more-icon">+${screenshots.length - 4}</span>
                        <span class="more-text">更多</span>
                    </div>
                `;
                moreBtn.addEventListener('click', () => this.toggleScreenshots());
                screenshotsContainer.appendChild(moreBtn);
            }
        }
    }

    toggleScreenshots() {
        this.showingAllScreenshots = !this.showingAllScreenshots;
        this.renderScreenshotDisplay();

        // 重新初始化懒加载
        setTimeout(() => {
            initializeLazyLoading();
        }, 100);
    }

    renderRatingBreakdown() {
        const ratingBreakdown = document.getElementById('ratingBreakdown');
        
        // Generate mock rating breakdown based on overall rating
        const overallRating = parseFloat(this.appData.rating) || 0;
        const breakdown = this.generateRatingBreakdown(overallRating);
        
        ratingBreakdown.innerHTML = breakdown.map((item, index) => `
            <div class="rating-row">
                <span class="rating-stars">${'★'.repeat(5 - index)}${'☆'.repeat(index)}</span>
                <div class="rating-bar">
                    <div class="rating-fill" style="width: ${item.percentage}%"></div>
                </div>
                <span class="rating-percentage">${item.percentage}%</span>
            </div>
        `).join('');
    }
    
    generateRatingBreakdown(overallRating) {
        // Generate realistic rating distribution based on overall rating
        const base = Math.max(0, overallRating - 2);
        return [
            { stars: 5, percentage: Math.round(base * 20 + Math.random() * 20) },
            { stars: 4, percentage: Math.round(base * 15 + Math.random() * 15) },
            { stars: 3, percentage: Math.round(base * 10 + Math.random() * 10) },
            { stars: 2, percentage: Math.round(base * 5 + Math.random() * 5) },
            { stars: 1, percentage: Math.round(base * 2 + Math.random() * 3) }
        ];
    }
    
    loadRelatedApps() {
        const relatedAppsGrid = document.getElementById('relatedAppsGrid');

        console.log('Loading related apps...');
        console.log('Current app data:', this.appData);
        console.log('All apps count:', this.allApps.length);

        // Get current app category
        const currentCategory = this.appData.type || this.appData.primaryGenreName || this.appData.categoryName;
        const currentAppId = this.appData.appId || this.appData.trackId;

        console.log('Current category:', currentCategory);
        console.log('Current app ID:', currentAppId);

        // Find apps in the same category
        const sameCategory = this.allApps.filter(app => {
            const appCategory = app.type || app.primaryGenreName || app.categoryName;
            const appId = app.appId || app.trackId;
            return appCategory === currentCategory && appId !== currentAppId;
        });

        console.log('Same category apps found:', sameCategory.length);

        // Get random 6 apps, or all available if less than 6
        const relatedApps = sameCategory
            .sort(() => Math.random() - 0.5)
            .slice(0, 6);

        if (relatedApps.length > 0) {
            relatedAppsGrid.innerHTML = '';
            relatedApps.forEach(app => {
                const cardElement = createAppCard(app, 'mini');
                relatedAppsGrid.appendChild(cardElement);
            });
        } else {
            // If no related apps found, show random apps
            const randomApps = this.allApps
                .filter(app => (app.appId || app.trackId) !== currentAppId)
                .sort(() => Math.random() - 0.5)
                .slice(0, 6);

            relatedAppsGrid.innerHTML = '';
            randomApps.forEach(app => {
                const cardElement = createAppCard(app, 'mini');
                relatedAppsGrid.appendChild(cardElement);
            });
        }

        // Initialize lazy loading for related apps
        setTimeout(() => {
            initializeLazyLoading();
        }, 100);
    }
    
    setupEventListeners() {
        const downloadBtn = document.getElementById('downloadBtn');
        const developerAppsBtn = document.getElementById('developerAppsBtn');

        // Update download button href with correct App Store URL
        if (downloadBtn && this.appData) {
            const appId = this.appData.appId || this.appData.trackId;
            if (appId) {
                downloadBtn.href = `https://apps.apple.com/app/id${appId}`;
            }
        }

        // Developer apps button
        if (developerAppsBtn) {
            developerAppsBtn.addEventListener('click', () => {
                this.viewDeveloperApps();
            });
        }
    }
    


    viewDeveloperApps() {
        const developer = this.appData.publisher || this.appData.artistName || this.appData.developer;
        if (developer) {
            window.location.href = `search.html?q=${encodeURIComponent(developer)}`;
        }
    }
    
    showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.remove('hidden');
    }
    
    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.add('hidden');
    }
    
    showError(message) {
        showNotification(message, 'error');
        // Redirect to home page after error
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 3000);
    }
}

// Initialize app detail page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing AppDetailPage');
    new AppDetailPage();
});
