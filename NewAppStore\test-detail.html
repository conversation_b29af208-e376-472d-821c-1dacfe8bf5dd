<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Detail Page</title>
</head>
<body>
    <h1>Test Detail Page</h1>
    <div id="test-output"></div>
    
    <script>
        async function loadData(url) {
            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Error loading data:', error);
                throw error;
            }
        }
        
        async function testDataLoading() {
            const output = document.getElementById('test-output');
            
            try {
                // Test loading the app data
                console.log('Loading app data...');
                const appData = await loadData('./data/appInfo/541933937.json');
                console.log('App data loaded:', appData);
                
                // Display key information
                output.innerHTML = `
                    <h2>App Data Test</h2>
                    <p><strong>App Name:</strong> ${appData.trackName || 'Not found'}</p>
                    <p><strong>Description:</strong> ${appData.description ? 'Found' : 'Not found'}</p>
                    <p><strong>Screenshots:</strong> ${appData.screenshotUrls ? appData.screenshotUrls.length : 0} found</p>
                    <p><strong>Icon:</strong> ${appData.artworkUrl100 || 'Not found'}</p>
                    
                    <h3>Screenshots:</h3>
                    <div style="display: flex; gap: 10px; overflow-x: auto;">
                        ${appData.screenshotUrls ? appData.screenshotUrls.slice(0, 3).map(url => 
                            `<img src="${url}" style="width: 100px; height: auto;" alt="Screenshot">`
                        ).join('') : 'No screenshots'}
                    </div>
                    
                    <h3>Description Preview:</h3>
                    <p style="max-height: 100px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;">
                        ${appData.description ? appData.description.substring(0, 200) + '...' : 'No description'}
                    </p>
                `;
                
            } catch (error) {
                console.error('Test failed:', error);
                output.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testDataLoading);
    </script>
</body>
</html>
