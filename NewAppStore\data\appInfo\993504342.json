{"artistViewUrl": "https://apps.apple.com/us/developer/lg-electronics-inc/id382800716?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/10/70/6f/10706fd1-fbaf-23f7-3460-edc059e9250d/AppIcon-0-0-1x_U007emarketing-0-8-0-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/10/70/6f/10706fd1-fbaf-23f7-3460-edc059e9250d/AppIcon-0-0-1x_U007emarketing-0-8-0-0-85-220.png/100x100bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/ff/47/e2/ff47e2cb-e3c2-988f-b927-52766cc6dc65/iOS_app_eng_00_main.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/e4/fa/10/e4fa101c-83d4-e4c6-f218-a5ffe0fcfd43/iOS_app_eng_01_home_tab.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/6f/7c/25/6f7c257b-4463-f707-2da9-efad43027a00/iOS_app_eng_02_discover_tab.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/2e/61/50/2e6150dd-d732-f681-a6ba-236617733d73/iOS_app_eng_03_chatbot.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/bd/46/85/bd4685d7-14c6-3893-face-baca7620fef8/iOS_app_eng_04_upgrade_center.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/27/85/aa/2785aa24-9a35-53a0-ba23-c823493e9b0e/iOS_app_eng_05_smart_routine.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/6c/92/af/6c92af93-98e1-8dbf-70cb-480aa2b9d090/iOS_app_eng_06_report.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/1d/8f/74/1d8f74d6-e228-8b59-1e7c-75f165b58b13/iOS_app_eng_07_smart_diagnosis.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/1f/90/46/1f904689-9093-3e2c-5964-b4545c990ce4/iOS_app_eng_08_manual.png/392x696bb.png"], "ipadScreenshotUrls": [], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/10/70/6f/10706fd1-fbaf-23f7-3460-edc059e9250d/AppIcon-0-0-1x_U007emarketing-0-8-0-0-85-220.png/512x512bb.jpg", "isGameCenterEnabled": false, "features": ["iosUniversal"], "advisories": [], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "kind": "software", "trackCensoredName": "LG ThinQ", "trackViewUrl": "https://apps.apple.com/us/app/lg-thinq/id993504342?uo=4", "contentAdvisoryRating": "4+", "averageUserRating": 4.5693, "languageCodesISO2A": ["SQ", "AM", "AR", "BS", "BG", "MY", "CA", "HR", "CS", "DA", "NL", "EN", "ET", "FI", "FR", "DE", "EL", "HE", "HI", "HU", "ID", "IT", "JA", "KK", "KO", "LV", "LT", "MK", "MS", "NB", "FA", "PL", "PT", "RO", "RU", "SR", "ZH", "SK", "SL", "ES", "SV", "TH", "ZH", "TR", "UK", "UZ", "VI"], "fileSizeBytes": "546338816", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 156706, "trackContentRating": "4+", "averageUserRatingForCurrentVersion": 4.5693, "price": 0, "releaseDate": "2016-07-18T00:31:53Z", "bundleId": "com.lgeha.nuts", "primaryGenreName": "Lifestyle", "primaryGenreId": 6012, "trackId": 993504342, "trackName": "LG ThinQ", "isVppDeviceBasedLicensingEnabled": true, "sellerName": "LG Electronics", "genreIds": ["6012"], "currentVersionReleaseDate": "2025-06-18T00:00:12Z", "releaseNotes": "Discover a multitude of diverse and convenient features in the latest version of the app.", "version": "5.1.11400", "wrapperType": "software", "currency": "USD", "description": "Connect your IoT home appliances to the LG ThinQ app.\nEnjoy effortless product control, smart care, and convenient automation in one simple solution.\n\n■ Discover the convenience of smart home appliances through the Home tab.\n - Control your IoT home appliances from anywhere with our app.\n - Get personalized recommendations for managing appliances based on usage history.\n■ Experience ThinQ UP appliances that evolve with you.\n - Customize the start and end melodies for different home appliances.\n - Download new cycles for your washing machine, dryer, styler, and dishwasher.\n■ Discover new ways to use your home appliances.\n - Check out special laundry care techniques in the Discover tab.\n■ Create smart routines to match your needs.\n - Automatically turn on the lights and air purifier when it’s time to wake up.\n - When you’re on vacation, automatically turn off products to save energy.\n■ Monitor your energy consumption data quickly.\n - Use Energy Monitoring to compare your power usage with your neighbors’.\n - Set energy saving goals and get usage status notifications to help save energy more efficiently.\n■ Handle everything from troubleshooting to service requests directly from the app.\n - Use the Smart Diagnosis function to check your product’s status.\n - Book a service visit from a professional engineer for an accurate diagnosis and inspection.\n■ Ask our AI-powered Chat with LG about ThinQ home appliances 24/7.\n - Our Chat with LG provides answers tailored to your product’s situation and condition.\n■ Conveniently reference LG home appliance manuals in one place.\n - Access a range of content, including function descriptions and essential usage solutions for products.\n\n※ Services and features may vary depending on your product model and your country or region of residence.\n\nThe following access permissions are necessary for the ThinQ app. \nAll the access permissions requested are optional access permission, so even if you do not allow them, you can still use the app except for the related services. (Including OS Request Permissions such as \"Siri & Search\", \"Notification\", \"Background App Refresh\", and \"Cellular Data\")\n\n[Optional Access Permissions]\n• Location\n- To find and connect to nearby Wi-Fi when registering the product.\n- To set and save the home location in Manage Home\n- To search for and use information about current locations, such as weather.\n- To check your current location in the \"Smart Routines\" function.\n\n• Camera\n- To take a profile picture\n- To share a home or account scanned from a QR code.\n- To take and attach photos in \"1:1 Inquiry.\"\n- To record and store purchase receipts when registering additional information about the product.\n- To use the AI oven Cooking Record feature.\n\n• Photo, Media, File\n- To use the smartphone's storage when downloading the information of the product to be connected\n- To take and attach photos in \"1:1 Inquiry.\"\n- To record and store purchase receipts when registering additional information about the product.\n- To view photo/video on your smartphone on the TV.\n\n• Media and Apple Music\n- To view photo/video on your smartphone on the TV.\n\n• Microphone\n- To check product status via Smart Diagnosis\n\n• Local Network\n- To obtain the device connecting Wi-Fi info when adding devices\n\n• Bluetooth\n- To find and connect to nearby Bluetooth devices when adding a product to the app.\n\n• HomeKit\n- To add IoT-based products\n\n• Notifications\n- Notifications are essential for receiving updates on product status, important notices, benefits, and information.", "minimumOsVersion": "16.0", "genres": ["Lifestyle"], "artistId": 382800716, "artistName": "LG Electronics, Inc.", "userRatingCount": 156706}